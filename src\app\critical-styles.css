/* Critical CSS for initial render */
:root {
  --primary: #3b82f6;
  --primary-content: #ffffff;
  --secondary: #8b5cf6;
  --accent: #06b6d4;
  --neutral: #6b7280;
  --base-100: #ffffff;
  --base-200: #f8fafc;
  --base-300: #f1f5f9;
  --base-content: #0f172a;

  /* Performance variables */
  --image-placeholder-color: #f1f1f1;
  --image-loading-bg: rgba(249, 250, 251, 0.5);
  --content-visibility-timeout: 0ms; /* Adjust based on testing */
}

/* Base styles */
body {
  margin: 0;
  padding: 0;
  font-family: var(--font-poppins), system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Use theme variables instead of base variables */
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Content visibility optimization for non-critical sections */
.defer-visibility {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px; /* Estimate height to prevent layout shifts */
}

/* Loading states */
.skeleton {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: var(--base-200);
  border-radius: 0.5rem;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: translateZ(0); /* Force GPU acceleration */
  }
  50% {
    opacity: 0.5;
    transform: translateZ(0); /* Force GPU acceleration */
  }
}

/* Layout */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Basic components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--primary-content);
}

.card {
  background-color: var(--base-200);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-body {
  padding: 1.5rem;
}

/* Loading spinner */
.loading {
  pointer-events: none;
  display: inline-block;
}

.loading-spinner {
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  border: 0.25rem solid currentColor;
  border-right-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg) translateZ(0);
  }
  to {
    transform: rotate(360deg) translateZ(0);
  }
}

/* Modern gradient element */
.bg-gradient-to-r {
  background-image: linear-gradient(
    to right,
    var(--brand-primary),
    var(--brand-secondary),
    var(--brand-accent)
  );
}

/* Image optimization styles */
img {
  max-width: 100%;
  height: auto;
}

/* Prevent layout shifts with aspect ratio containers */
.aspect-ratio-container {
  position: relative;
  width: 100%;
  height: 0;
  overflow: hidden;
}

.aspect-ratio-1-1 {
  padding-bottom: 100%; /* 1:1 aspect ratio */
}

.aspect-ratio-16-9 {
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.aspect-ratio-4-3 {
  padding-bottom: 75%; /* 4:3 aspect ratio */
}

.aspect-ratio-container > img,
.aspect-ratio-container > picture,
.aspect-ratio-container > div {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Image placeholder */
.img-placeholder {
  background-color: var(--image-placeholder-color);
  position: relative;
  overflow: hidden;
}

/* Optimize animation performance with will-change */
.img-animate {
  will-change: opacity, transform;
}

/* Mobile-specific styles for community feed */
@media (max-width: 640px) {
  .container {
    padding-left: 0;
    padding-right: 0;
  }

  /* Community cards mobile optimization - full width */
  .community-card {
    margin-bottom: 1rem;
    width: calc(100vw - 1rem);
    max-width: calc(100vw - 1rem);
    border-radius: 0;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }

  /* Ensure grid takes full width on mobile */
  .grid {
    width: 100vw;
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }

  .grid.grid-cols-1 {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 0;
  }

  /* Header mobile optimization */
  .navbar {
    padding: 0.5rem 0;
  }

  .navbar .container {
    padding: 0 0.5rem;
  }

  /* Search bar mobile optimization */
  .search-container {
    margin-bottom: 1rem;
  }

  /* Typography adjustments for mobile */
  h1 {
    font-size: 1.875rem !important; /* 30px */
    line-height: 2.25rem !important; /* 36px */
  }

  h2 {
    font-size: 1.25rem !important; /* 20px */
    line-height: 1.75rem !important; /* 28px */
  }

  /* Button sizing for mobile */
  .btn {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
  }

  /* Dropdown adjustments */
  .dropdown-content {
    left: 0 !important;
    right: 0 !important;
    width: auto !important;
    margin: 0.5rem;
  }

  /* Improved touch targets */
  .btn,
  .dropdown > div,
  .community-card {
    min-height: 44px;
  }

  /* Mobile drawer adjustments */
  .drawer-content {
    width: 85vw !important;
    max-width: 320px;
  }

  .drawer-content h2 {
    font-size: 1.125rem !important;
  }

  .drawer-content .space-y-2 > * {
    margin-bottom: 0.5rem;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .container {
    padding-left: 0;
    padding-right: 0;
  }

  .community-card {
    width: calc(100vw - 0.5rem);
    max-width: calc(100vw - 0.5rem);
    margin-left: 0.25rem;
    margin-right: 0.25rem;
  }

  h1 {
    font-size: 1.5rem !important; /* 24px */
    line-height: 2rem !important; /* 32px */
  }

  /* Drawer adjustments for very small screens */
  .drawer-content {
    width: 90vw !important;
    max-width: 280px;
  }
}

/* Drawer styles */
.drawer-content {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.drawer-content::-webkit-scrollbar {
  width: 6px;
}

.drawer-content::-webkit-scrollbar-track {
  background: transparent;
}

.drawer-content::-webkit-scrollbar-thumb {
  background-color: var(--text-secondary);
  border-radius: 3px;
  opacity: 0.5;
}

.drawer-content::-webkit-scrollbar-thumb:hover {
  opacity: 0.8;
}

/* Drawer animations */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}
