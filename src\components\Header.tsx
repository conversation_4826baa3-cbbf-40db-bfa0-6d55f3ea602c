"use client";
import { useSession, signOut } from "next-auth/react";
import { useEffect, useState } from "react";
import { useNotification } from "./Notification";
import { useSettingsModal } from "./modals/SettingsModalProvider";
import Link from "next/link";
import Image from "next/image";
import { User, ChevronDown, Compass, Plus, X, Menu } from "lucide-react";

import CommunityIcon from "./communitynav/CommunityIcon";
import { usePathname } from "next/navigation";
import MessageIcon from "./messages/MessageIcon";
import NotificationIcon from "./notifications/NotificationIcon";
import ThemeSwitcher from "./ThemeSwitcher";
import ProfileAvatar from "./ProfileAvatar";

interface Community {
  _id: string;
  name: string;
  slug: string;
  iconImageUrl?: string;
  role: string;
}

export default function Header() {
  const { data: session } = useSession();
  const { showNotification } = useNotification();
  const { openUserSettings } = useSettingsModal();

  const [userCommunities, setUserCommunities] = useState<Community[]>([]);
  const [currentCommunity, setCurrentCommunity] = useState<Community | null>(
    null
  );
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const pathname = usePathname();

  // No debug logging in production

  // Fetch user communities
  // Function to preload community icons
  const preloadCommunityIcons = (communities: Community[]) => {
    communities.forEach((community) => {
      if (community.iconImageUrl && community.iconImageUrl.trim() !== "") {
        const img = new window.Image();
        img.src = community.iconImageUrl;
      }
    });
  };

  // Effect to detect current community from pathname
  useEffect(() => {
    if (pathname) {
      const match = pathname.match(/\/Newcompage\/([^\/]+)/);
      if (match && match[1]) {
        const communitySlug = match[1];
        // Find the community in userCommunities
        const community = userCommunities.find((c) => c.slug === communitySlug);
        if (community) {
          setCurrentCommunity(community);
        } else {
          // If not found in userCommunities, fetch it
          const fetchCommunity = async () => {
            try {
              const response = await fetch(`/api/community/${communitySlug}`);
              if (response.ok) {
                const data = await response.json();
                setCurrentCommunity({
                  _id: data._id,
                  name: data.name,
                  slug: data.slug,
                  iconImageUrl: data.iconImageUrl || "",
                  role: "visitor",
                });
              }
            } catch (error) {
              // Error handling silently
            }
          };
          fetchCommunity();
        }
      } else {
        setCurrentCommunity(null);
      }
    }
  }, [pathname, userCommunities]);

  useEffect(() => {
    const fetchUserCommunities = async () => {
      if (session?.user) {
        try {
          const response = await fetch("/api/user/communities");
          if (response.ok) {
            const data = await response.json();
            setUserCommunities(data);

            // Preload all community icons
            preloadCommunityIcons(data);
          }
        } catch (error) {
          // Error handling silently
        }
      }
    };

    fetchUserCommunities();
  }, [session]);

  // Close drawer when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isDrawerOpen &&
        !(event.target as Element).closest(".drawer-content") &&
        !(event.target as Element).closest(".drawer-toggle")
      ) {
        setIsDrawerOpen(false);
      }
    };

    if (isDrawerOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "hidden"; // Prevent background scroll
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "unset";
    };
  }, [isDrawerOpen]);

  // Close drawer on route change
  useEffect(() => {
    setIsDrawerOpen(false);
  }, [pathname]);

  const handleSignOut = async () => {
    try {
      await signOut();
      showNotification("Sign out successful", "success");
    } catch (error) {
      showNotification("Sign out failed", "error");
    }
  };

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  return (
    <>
      <div
        className="navbar sticky top-0 z-40 shadow-sm border-b transition-colors duration-300"
        style={{
          backgroundColor: "var(--bg-secondary)",
          borderColor: "var(--border-color)",
        }}
      >
        <div className="container mx-auto px-2 sm:px-4 flex items-center justify-between">
          <div className="flex-none flex items-center gap-1 sm:gap-2">
            <Link
              href="/"
              className="btn btn-ghost text-base sm:text-xl normal-case font-bold relative group transition-colors duration-200 px-2 sm:px-4"
              prefetch={true}
              style={{ color: "var(--text-primary)" }}
              onClick={() =>
                showNotification("Welcome to TheTribelab", "success")
              }
            >
              <span className="relative z-10 hidden sm:inline">
                TheTribelab
              </span>
              <span className="relative z-10 sm:hidden">TTL</span>
              <span
                className="absolute bottom-0 left-0 w-0 h-0.5 transition-all duration-300 group-hover:w-full"
                style={{ backgroundColor: "var(--brand-primary)" }}
              ></span>
            </Link>

            {/* Communities Drawer Toggle - Mobile Only */}
            {session && (
              <button
                onClick={toggleDrawer}
                className="drawer-toggle btn btn-ghost btn-sm normal-case flex items-center gap-2 rounded-lg transition-colors duration-200 px-2 sm:px-3 md:hidden"
                style={{
                  color: "var(--text-secondary)",
                  backgroundColor: "transparent",
                  border: "1px solid var(--border-color)",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                  e.currentTarget.style.borderColor = "var(--brand-primary)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent";
                  e.currentTarget.style.borderColor = "var(--border-color)";
                }}
                aria-label="Toggle communities menu"
                title="Open communities menu"
              >
                <Menu
                  size={20}
                  className="w-5 h-5 sm:w-6 sm:h-6 flex-shrink-0"
                  style={{ color: "var(--brand-primary)" }}
                />
                <span className="hidden lg:inline text-xs font-medium">
                  Menu
                </span>
              </button>
            )}
          </div>

          {/* Desktop Navigation - Hidden on Mobile */}
          {session && (
            <div className="hidden md:flex flex-1 justify-center">
              <div className="flex items-center gap-4">
                <div className="dropdown dropdown-hover">
                  <div
                    tabIndex={0}
                    role="button"
                    className="btn btn-ghost normal-case flex items-center gap-2 rounded-lg transition-colors duration-200"
                    style={{ color: "var(--text-primary)" }}
                  >
                    <Compass size={18} />
                    <span className="font-medium">Communities</span>
                    <ChevronDown size={16} />
                  </div>
                  <ul
                    tabIndex={0}
                    className="dropdown-content z-[1] menu p-2 shadow-lg rounded-box w-64 mt-2"
                    style={{
                      backgroundColor: "var(--bg-secondary)",
                      borderColor: "var(--border-color)",
                      border: "1px solid",
                    }}
                  >
                    <li>
                      <Link
                        href="/community-feed"
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-base-200 transition-colors duration-200"
                        style={{ color: "var(--text-primary)" }}
                      >
                        <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                          <Compass size={16} className="text-primary" />
                        </div>
                        <span className="font-medium">
                          Discover Communities
                        </span>
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/communityform"
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-base-200 transition-colors duration-200"
                        style={{ color: "var(--text-primary)" }}
                      >
                        <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                          <Plus size={16} className="text-primary" />
                        </div>
                        <span className="font-medium">Create Community</span>
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          <div className="flex-1 flex justify-end">
            <div className="flex items-stretch gap-1 sm:gap-2">
              {/* Message Icon */}
              {session && <MessageIcon />}
              {/* Notification Icon */}
              {session && <NotificationIcon />}
              {/* Theme Switcher */}
              <ThemeSwitcher />
              <div className="dropdown dropdown-end">
                <div
                  tabIndex={0}
                  role="button"
                  aria-label="User menu"
                  title="User menu"
                  className="btn btn-ghost btn-circle avatar border-2 transition-colors duration-300 w-8 h-8 sm:w-10 sm:h-10"
                  style={{
                    borderColor: "var(--border-color)",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = "var(--brand-primary)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = "var(--border-color)";
                  }}
                >
                  {session?.user ? (
                    <ProfileAvatar
                      imageUrl={session.user.profileImage}
                      name={session.user.name || session.user.username}
                      email={session.user.email}
                      size="sm"
                    />
                  ) : (
                    <User
                      className="w-4 h-4 sm:w-5 sm:h-5"
                      style={{ color: "var(--text-secondary)" }}
                    />
                  )}
                </div>
                <ul
                  tabIndex={0}
                  className="dropdown-content z-[1] menu p-2 shadow-lg rounded-box w-48 sm:w-64 mt-4"
                  style={{
                    backgroundColor: "var(--dropdown-bg)",
                    color: "var(--text-primary)",
                    maxWidth: "250px",
                    overflow: "hidden",
                  }}
                >
                  {session ? (
                    <>
                      <li className="px-4 py-1">
                        <span className="text-xs sm:text-sm opacity-70 truncate">
                          {session.user.id.split("@")[0]}
                        </span>
                      </li>
                      <li className="divider my-1"></li>
                      <li>
                        <Link
                          href={"/profile"}
                          className="px-4 py-2 block w-full transition-colors duration-200 rounded-lg text-sm sm:text-base"
                          style={{ color: "var(--text-primary)" }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor =
                              "var(--hover-bg)";
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor =
                              "transparent";
                          }}
                          onClick={() =>
                            showNotification("Create your Community", "info")
                          }
                        >
                          Profile
                        </Link>
                      </li>
                      <li>
                        <Link
                          href={"/communityform"}
                          className="px-4 py-2 block w-full transition-colors duration-200 rounded-lg text-sm sm:text-base"
                          style={{ color: "var(--text-primary)" }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor =
                              "var(--hover-bg)";
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor =
                              "transparent";
                          }}
                          onClick={() =>
                            showNotification("Create your Community", "info")
                          }
                        >
                          Create Community
                        </Link>
                      </li>
                      <li>
                        <button
                          type="button"
                          className="px-4 py-2 block w-full transition-colors duration-200 rounded-lg text-left text-sm sm:text-base"
                          style={{ color: "var(--text-primary)" }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor =
                              "var(--hover-bg)";
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor =
                              "transparent";
                          }}
                          onClick={() => {
                            openUserSettings();
                            showNotification("Opening Settings", "info");
                          }}
                        >
                          Settings
                        </button>
                      </li>
                      <li
                        className="divider my-1"
                        style={{ borderColor: "var(--border-color)" }}
                      ></li>
                      <li className="items-center px-4 flex justify-center">
                        <button
                          type="button"
                          onClick={handleSignOut}
                          className="btn btn-primary btn-sm relative overflow-hidden transition-all duration-200"
                          style={{
                            backgroundColor: "var(--brand-primary)",
                            color: "var(--primary-content)",
                            border: "none",
                          }}
                        >
                          Log Out
                        </button>
                      </li>
                    </>
                  ) : (
                    <li>
                      <Link
                        href="/login"
                        className="px-4 py-2 block w-full transition-colors duration-200 rounded-lg text-sm sm:text-base"
                        style={{ color: "var(--text-primary)" }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor =
                            "var(--hover-bg)";
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = "transparent";
                        }}
                        onClick={() =>
                          showNotification("Please sign in to continue", "info")
                        }
                      >
                        Login
                      </Link>
                    </li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Drawer Overlay */}
      {isDrawerOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-50 transition-opacity duration-300"
          onClick={() => setIsDrawerOpen(false)}
        />
      )}

      {/* Drawer */}
      <div
        className={`drawer-content fixed top-0 left-0 h-full w-80 sm:w-96 z-50 transform transition-transform duration-300 ease-in-out ${isDrawerOpen ? "translate-x-0" : "-translate-x-full"}`}
        style={{
          backgroundColor: "var(--bg-secondary)",
          borderRight: "1px solid var(--border-color)",
          boxShadow: "var(--shadow-lg)",
        }}
      >
        {/* Drawer Header */}
        <div
          className="flex items-center justify-between p-4 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <h2
            className="text-lg font-semibold"
            style={{ color: "var(--text-primary)" }}
          >
            Communities
          </h2>
          <button
            onClick={() => setIsDrawerOpen(false)}
            className="btn btn-ghost btn-sm btn-circle"
            style={{ color: "var(--text-secondary)" }}
            aria-label="Close communities menu"
            title="Close communities menu"
          >
            <X size={20} />
          </button>
        </div>

        {/* Drawer Content */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-2">
            {/* Discover Communities */}
            <Link
              href="/community-feed"
              className="flex items-center gap-3 p-3 rounded-lg hover:bg-base-200 transition-colors duration-200"
              style={{ color: "var(--text-primary)" }}
              onClick={() => setIsDrawerOpen(false)}
            >
              <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                <Compass size={20} className="text-primary" />
              </div>
              <span className="font-medium">Discover Communities</span>
            </Link>

            {/* Create Community */}
            <Link
              href="/communityform"
              className="flex items-center gap-3 p-3 rounded-lg hover:bg-base-200 transition-colors duration-200"
              style={{ color: "var(--text-primary)" }}
              onClick={() => setIsDrawerOpen(false)}
            >
              <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                <Plus size={20} className="text-primary" />
              </div>
              <span className="font-medium">Create Community</span>
            </Link>

            {/* User Communities */}
            {userCommunities.length > 0 && (
              <>
                <div className="pt-4 pb-2">
                  <h3 className="text-sm font-medium text-gray-500 px-3">
                    Your Communities
                  </h3>
                </div>
                {userCommunities.map((community) => {
                  const isCurrentCommunity =
                    currentCommunity?.slug === community.slug;
                  return (
                    <Link
                      key={community._id}
                      href={`/Newcompage/${community.slug}`}
                      className={`flex items-center gap-3 p-3 rounded-lg hover:bg-base-200 transition-colors duration-200 ${
                        isCurrentCommunity
                          ? "bg-primary/10 border-l-4 border-primary"
                          : ""
                      }`}
                      style={{ color: "var(--text-primary)" }}
                      onClick={() => setIsDrawerOpen(false)}
                    >
                      <CommunityIcon
                        iconUrl={community.iconImageUrl}
                        name={community.name}
                        size="md"
                        className="hover:scale-105 transition-transform duration-200"
                        priority={true}
                      />
                      <div className="flex-1">
                        <div className="font-medium truncate">
                          {community.name}
                        </div>
                        {isCurrentCommunity && (
                          <div className="text-xs text-primary">Current</div>
                        )}
                      </div>
                    </Link>
                  );
                })}
              </>
            )}

            {/* Empty State */}
            {userCommunities.length === 0 && (
              <div className="px-3 py-8 text-center">
                <p className="text-sm text-gray-500 mb-4">
                  You haven't joined any communities yet
                </p>
                <Link
                  href="/community-feed"
                  className="btn btn-primary btn-sm"
                  onClick={() => setIsDrawerOpen(false)}
                >
                  Discover Communities
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
